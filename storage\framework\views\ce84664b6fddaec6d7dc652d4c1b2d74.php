	<script>
	    // Function to update image src
	    function updateImageSrc(imageElement) {
	        if (!imageElement.attr('src')) {
	            imageElement.attr('src', "<?php echo e(secure_url('public/uploads/dummy/dummy.jpg')); ?>");
	        }

	        var currentSrc = imageElement.attr('src');

	        if (!currentSrc || currentSrc === "<?php echo e(secure_url('public')); ?>" || currentSrc === "<?php echo e(secure_url('public/')); ?>") {
	            imageElement.attr('src', "<?php echo e(secure_url('public/uploads/dummy/dummy.jpg')); ?>");
	        }
	    }

	    // Update images on page load
	    $(document).ready(function() {
	        $('img').each(function() {
	            updateImageSrc($(this));
	        });
	    });

	    // Listen for AJAX calls and update images in dynamically added content
	    $(document).ajaxComplete(function() {
	        $('img').each(function() {
	            updateImageSrc($(this));
	        });
	    });

	    
        
	    // Wait for the document to be ready
	    $(document).ready(function() {
	        $.ajax({
	            url: "<?php echo e(secure_url('get-data/captcha-refresh')); ?>",
	            type: "post",
	            data: {
	                _token: "<?php echo e(csrf_token()); ?>"
	            },
	            dataType: 'json',
	            success: function(response) {
	                $('.captcha-image').html(response.captcha);
	            },
	            error: function(data) {
	                console.log(data);
	                return false;
	            }
	        });
	    });
		
		
/**********************************************************Load Doctor and Speciality Options Based on Hospital*******************/
$('.appointment-hospital-filter').on('change', function () {
	// Get the selected value
	var hospital_id = $(this).val();

	// Execute the AJAX request
	$.ajax({
		url: "<?php echo e(secure_url('get-data/get-doctor-and-speciality-for-filter')); ?>",
		type: "post",
		data: {
			_token: "<?php echo e(csrf_token()); ?>",
			hospital_id: hospital_id // Include any additional data you need to send
		},
		dataType: 'json',
		success: function (data) {
			$('.appointment-speciality-doctor-filter').empty();
			
			$('.appointment-speciality-doctor-filter').append('<option></option>');
			
			// Append new option groups
			$.each(data, function (index, group) {
			  var optgroup = $('<optgroup label="' + group.label + '"></optgroup>');

			  // Append options to the group
			  $.each(group.options, function (i, option) {
				optgroup.append('<option value="' + option.value + '" ' + option.selected + '>' + option.text + '</option>');
			  });

			  // Append the group to the select element
			  $('.appointment-speciality-doctor-filter').append(optgroup);
			});

			// Update Select2
			$('.appointment-speciality-doctor-filter').trigger('change');
		},
		error: function (data) {
			// Handle the error
			console.log(data);
		}
	});
});
	
$(document).ready(function() {
    $('.appointment-hospital-filter').trigger('change');
});
		
function searchData(str) {
	$.ajax({
		url: "<?php echo e(secure_url('get-data/search')); ?>",
		type: "post",
		data: {
			_token: "<?php echo e(csrf_token()); ?>",
			search_data: str
		},
		dataType: 'json',
		success: function(response) {
			$('.search-data').html(response['data']);
		},
		error: function(data) {
			console.log(data);
			return false;
		}
	});
}

		
  function playVideoTestimonial(video_source,video_id,element) {
        if(video_source=="1")
		{
        	var video_data = '<iframe width="100%" height="415" src="https://www.youtube.com/embed/' + video_id + '" id="video_id" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen ></iframe>';
		}
	  	else
		{
			var video_data='<iframe src="https://www.facebook.com/plugins/video.php?height=476&href=' + video_id + '" width="100%" height="600" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true" allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share" allowFullScreen="true"></iframe>';
		}

        $(element).html(video_data);
    }
		
function playVideo(video_source,video_id) {

		if(video_source=="1")
		{
        	var video_data = '<iframe width="100%" height="415" src="https://www.youtube.com/embed/' + video_id + '" id="video_id" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen ></iframe>';
		}
	  	else
		{
			var video_data='<iframe src="https://www.facebook.com/plugins/video.php?height=476&href=' + video_id + '" width="100%" height="600" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true" allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share" allowFullScreen="true"></iframe>';
		}



        $('.popup-video').html(video_data);

        $('#videoPoupModal').modal('show');

    }


document.addEventListener("DOMContentLoaded", function() {
  const starRatingElements = document.querySelectorAll(".star-rating");

  starRatingElements.forEach(function(starRatingElement) {
    const rating = parseFloat(starRatingElement.dataset.rating);
    const numFullStars = Math.floor(rating);
    const remainder = rating - numFullStars;
    const starPercentage = (numFullStars + remainder) * 100;
    let starsHtml = rating;

    for (let i = 0; i < 5; i++) {
      if (i < numFullStars) {
        starsHtml += '<i class="fa fa-solid fa-star ms-1" style="color: gold;"></i>'; // Full star
      } else if (i === numFullStars && remainder > 0) {
        starsHtml += `<i class="fa fa-solid fa-star-half ms-1" style="color: gold;"></i>`; // Partial star
      } else {
        starsHtml += '<i class="fa fa-solid fa-star ms-1"></i>'; // Empty star
      }
    }

    starRatingElement.innerHTML = starsHtml;
   // starRatingElement.style.width = `${starPercentage}%`; // Set the width of the container
  });
});


</script>

<script>
    /*document.addEventListener("DOMContentLoaded", function() {
      var inputs = document.querySelectorAll("input[type='submit'], button[type='submit']");

      inputs.forEach(function(input) {
        input.addEventListener("click", function() {
          var processingMsg = "<p>We are processing your request <i class='fas fa-spinner fa-spin'></i></p>";
          input.parentNode.replaceChild(parseHTML(processingMsg), input);
          // Perform any additional processing here...
        });
      });

      // Function to parse HTML string into DOM elements
      function parseHTML(html) {
        var t = document.createElement('template');
        t.innerHTML = html.trim();
        return t.content.firstChild;
      }
    });*/
  </script>

<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
<script type="text/javascript">
	function googleTranslateElementInit() {
		new google.translate.TranslateElement({
			pageLanguage: 'en',
			layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
			includedLanguages: 'en,hi,pa' // Specify the languages you want to include
		}, 'google_translate_element');
	}
	$('#google_translate_element').mouseover(function(){
	  $('.VIpgJd-ZVi9od-xl07Ob-lTBxed').attr("href","javascript:void(0)");
	});
</script>



<script>
/**********************************************************Load Doctor and Speciality Options Based on Hospital*******************/
$('.appointment-hospital').on('change', function () {
	// Get the selected value
	var hospital_id = $(this).val();

	// Execute the AJAX request
	$.ajax({
		url: "<?php echo e(secure_url('process-appointment/get-doctor-and-speciality-for-selection')); ?>",
		type: "post",
		data: {
			_token: "<?php echo e(csrf_token()); ?>",
			hospital_id: hospital_id // Include any additional data you need to send
		},
		dataType: 'json',
		success: function (data) {
			$('.appointment-speciality-doctor').empty();
			
			// Append new option groups
			$.each(data, function (index, group) {
			  var optgroup = $('<optgroup label="' + group.label + '"></optgroup>');

			  // Append options to the group
			  $.each(group.options, function (i, option) {
				optgroup.append('<option value="' + option.value + '" ' + option.selected + '>' + option.text + '</option>');
			  });

			  // Append the group to the select element
			  $('.appointment-speciality-doctor').append(optgroup);
			});

			// Update Select2
			$('.appointment-speciality-doctor').trigger('change');
			setTimeout(function() {
				$('.select2-box').trigger('select2:close');
			}, 10);
		},
		error: function (data) {
			// Handle the error
			console.log(data);
		}
	});
});
	
$(document).ready(function() {
    $('.appointment-hospital').trigger('change');
});
</script>
<script>
/**********************************************************Submit Appointment Form*******************/
function submitAppointmentForm() 
{
	var formData = $("#appointment-form").serialize();

	$.ajax({
		type: "POST",
		url: $("#appointment-form").attr("action"), // Use the action attribute of the form
		data: formData,
		dataType: 'json',
		success: function(response) {
			$('.failed-appointment-msg').hide();
			if(response['msg']=='NA')
				getDoctor();
			else
			{
				$('.'+response['cls']).show();
				$('.'+response['cls']).html(response['msg']);
			}
		},
		error: function(error) {
			// Handle error
			console.error(error);
		}
	});
}
</script>


<script>
/**********************************************************Diagnosis Cart Management*******************/
function addToCartDiagnosis(id,type) 
{

	$.ajax({
		type: "POST",
		url: "<?php echo e(secure_url('get-data/add-to-cart-diagnosis')); ?>",
		type: "post",
		data: {
			_token: "<?php echo e(csrf_token()); ?>",
			diagnosis_id: id,
			diagnosis_type: type
		},
		dataType: 'json',
		success: function(response) {
			if(response['status'] == '1')  
				toastr.success("Item successfully added to the cart!");  
			else if(response['status'] == '2')  
				toastr.warning("This item is already in your cart.");  
			else  
				toastr.error("Failed to add item to the cart. Please try again!");  
			
			addToCartDiagnosisCount();
		},
		error: function(error) {
			// Handle error
			console.error(error);
		}
	});
}
	
function addToCartDiagnosisCount() 
{
    $.ajax({
        type: "POST",
        url: "<?php echo e(secure_url('get-data/add-to-cart-diagnosis-count')); ?>",
        data: {
            _token: "<?php echo e(csrf_token()); ?>"
        },
        dataType: 'json',
        success: function(response) {
            $('.cart-count').html(response['count']); 
        },
        error: function(error) {
            console.error(error);
        }
    });
}
	
$(document).ready(function() {
	addToCartDiagnosisCount();
});
	
	
function removeFromCartDiagnosis(id, type) 
{
    $.ajax({
        type: "POST",
        url: "<?php echo e(secure_url('get-data/remove-from-cart-diagnosis')); ?>",
        data: {
            _token: "<?php echo e(csrf_token()); ?>",
            diagnosis_id: id,
            diagnosis_type: type
        },
        dataType: 'json',
        success: function(response) {
            if(response['status'] == '1')  
                toastr.success("Item successfully removed from the cart!");  
            else if(response['status'] == '2')  
                toastr.warning("This item was not found in your cart.");  
            else  
                toastr.error("Failed to remove item from the cart. Please try again!");  

            location.reload(); 
        },
        error: function(error) {
            console.error(error);
        }
    });
}


</script>
<?php /**PATH F:\var\www\html\resources\views/website/layouts/partials/extra_js.blade.php ENDPATH**/ ?>