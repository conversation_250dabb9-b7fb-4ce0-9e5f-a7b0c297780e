

<?php $__env->startSection( 'content' ); ?>



<?php

$BANNER=App\Http\Controllers\Controller::getBanner(1);

?>

<section class="position-relative home-banner">
	<div class="owl-carousel-wrapper">
		<div class="owl-carousel dots-inside dots-horizontal-center show-dots-hover nav-inside nav-inside-plus nav-dark nav-md nav-font-size-md show-nav-hover mb-0" data-plugin-options="{'responsive': {'0': {'items': 1}, '479': {'items': 1}, '768': {'items': 1}, '979': {'items': 1}, '1199': {'items': 1}}, 'loop': true, 'autoHeight': false, 'margin': 0, 'dots': true, 'dotsVerticalOffset': '-150px', 'nav': false, 'autoplay': true, 'autoplayTimeout': 9000, 'autoplayHoverPause': true, 'rewind': true}">
			<?php $__currentLoopData = $BANNER; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $DATA): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
			<div class="banner-item">
				<a href="<?php echo e(empty($DATA->banner_url)?'javascript:void(0);':$DATA->banner_url); ?>" <?=$DATA->banner_type=='1'?'onclick="showCustomEnquiry(\''.$DATA->banner_title.'\')"':'';?>>
					<img src="<?php echo e(secure_url('public/'.$DATA->banner_image_desktop)); ?>" alt="<?php echo e($DATA->banner_title); ?>" class="img-fluid d-lg-block d-none">
					<img src="<?php echo e(secure_url('public/'.$DATA->banner_image_mobile)); ?>" alt="<?php echo e($DATA->banner_title); ?>" class="img-fluid d-lg-none d-block">
				</a>
			</div>
			<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
		</div>
	</div>
</section>


<?php

$WIDGET=App\Http\Controllers\Controller::getWidget("highlight");

?>

<section class="banner-layout banner-layout-bg">
	<div class="container">
		<div class="row">
			<?php $__currentLoopData = $WIDGET; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $DATA): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
			<?php
			$DETAILS=json_decode($DATA->widget_details,true);
			?>
			<div class="col-lg-2 col-md-4 col-4 mb-xl-0 mb-lg-0 mb-md-3 mb-3">
				<a href="<?php echo e(!empty($DETAILS['url'])?$DETAILS['url']:"javascript:void(0);"); ?>" class="text-decoration-none">
					<div class="highlight-outer text-center">
						<div class="highlight-item">
							<img src="<?php echo e(secure_url('public/'.$DETAILS['featured_image'])); ?>" alt="<?php echo e($DATA->widget_title); ?>" class="img-fluid">
						</div>
						<div class="highlight-text">
							<h3><?php echo e($DATA->widget_title); ?></h3>
						</div>
					</div>
				</a>
			</div>
			<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
		</div>
	</div>
</section>

<section class="section">
	<div class="container banner-form">
		<div class="row">
			<div class="col-xl-12 col-lg-12 mb-xl-0 mb-lg-0 mt-xl-0 mt-lg-0 mb-3 mt-3">
				<div class="banner-form-area">
					<form action="<?php echo e(secure_url('process-appointment/go-to-appointment')); ?>" method="post">
						<?php echo csrf_field(); ?>
						<div class="row">
							<div class="form-group my-lg-auto col-lg-3">
								<div class="select-box hospital-select">
									<select class="select2-box form-control appointment-hospital" style="width: 100%;" name="appointment_hospital" required>
										<option></option>
										<?php
										$HOSPITAL=App\Http\Controllers\Controller::getHospitalWhichINHIS();
										?>
										<?php $__currentLoopData = $HOSPITAL; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $DATA): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
										<option value="<?php echo e($DATA->hospital_id_his); ?>"><?php echo e($DATA->hospital_title); ?></option>
										<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
									</select>
								</div>
							</div>

							<div class="form-group my-lg-auto col-lg-3">
								<div class="select-box doctor-select">
									<select class="select2-box form-control appointment-speciality-doctor" style="width: 100%;" name="appointment_speciality_doctor[]" multiple="multiple" required>
									</select>
								</div>
							</div>

							<div class="form-group my-lg-auto col-lg-3">
								<div class="calender-field">
									<img src="img/calendar.png" alt="Calender Icon" class="field-date">
									<input type="text" placeholder="Select Date" class="form-control datepicker" name="appointment_date" autocomplete="off">
								</div>
							</div>
							<div class="form-group my-lg-auto col-lg-3">
								<input class="btn btn-primary form-btn w-100 py-lg-2 text-3-4" value="Book an Appointment" type="submit">
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</section>


<section class="home-about-section about-section-bg homehospital-network">
	<div class="section-spacing section3-line-bg">
		<div class="container-fluid">
			<div class="row">
				<div class="col-lg-4 col-md-6 col-sm-12 col-12 px-lg-0  px-md-0 my-lg-auto">
					<div class="home-about-content about-content-bg">
						<h1><?php echo e($PAGE_DATA['hospital_section_title']); ?></h1>
						<?php echo $PAGE_DATA['hospital_section_content']; ?>

					</div>
				</div>
				<?php
				$HOSPITAL=App\Http\Controllers\Controller::getHospital();
				?>
				<div class="col-lg-8 col-md-6 col-sm-12 col-12 px-lg-0 px-md-0">
					<div id="hospitalNetwork" class="owl-carousel owl-theme mb-0" data-plugin-options="{'responsive': {'0': {'items': 1}, '479': {'items': 1}, '768': {'items': 2}, '992': {'items': 3}, '1199': {'items': 3.5}}, 'loop': true, 'autoplay': true, 'dots': true, 'autoHeight': false, 'margin': 10}">
						<?php $__currentLoopData = $HOSPITAL; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $DATA): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
						<div class="hospital-card">
							<div class="hospital-image">
								<a href="<?php echo e(secure_url('hospital/'.$DATA->hospital_slug)); ?>">
									<img src="<?php echo e(secure_url('public/'.$DATA->hospital_image)); ?>" alt="<?php echo e($DATA->hospital_title); ?>" class="img-fluid">
								</a>
							</div>
							<div class="hospital-card-content">
								<div class="d-flex align-items-center mt-3 mb-3">
									<img src="img/icon1.png" alt="Hospital Icon" class="me-2">
									<a href="<?php echo e(secure_url('hospital/'.$DATA->hospital_slug)); ?>" class="text-decoration-none">
										<h2><?php echo e($DATA->hospital_title); ?></h2>
									</a>
								</div>
								<div class="d-flex mb-2">
									<i class="icon-location-pin location-icon"></i>
									<p><?php echo e($DATA->hospital_address); ?></p>
								</div>
								<!--<div class="d-flex mb-2">
									<div class="icons-bg me-2">
										<i class="fas fa-phone text-white text-2"></i>
									</div>
									<a href="tel:<?php echo e($DATA->hospital_appointment_contact_no); ?>"><?php echo e($DATA->hospital_appointment_contact_no); ?></a>
								</div>-->
								<a href="<?php echo e(secure_url('hospital/'.$DATA->hospital_slug)); ?>">View Details</a>
								<div class="d-flex align-items-center">
									<img src="img/google.png" alt="Google Logo" class="me-1">
									<div class="star-rating" data-rating="<?php echo e($DATA->hospital_rating); ?>"></div>
								</div>
							</div>
						</div>
						<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>



<section class="why-choose-section position-relative">
	<div class="section-spacing">
		<div class="container">
			<div class="row justify-content-end">
				<div class="col-lg-10">
					<div class="why-choose-wrap">
						<div class="layout-box">
							<div class="left-image-box">
								<img src="<?php echo e(secure_url('public/'.$PAGE_DATA['usp_section_image'])); ?>" alt="<?php echo e($PAGE_DATA['usp_section_title']); ?>" class="img-fluid">
							</div>
							<div class="right-content-box">
								<h1><?php echo e($PAGE_DATA['usp_section_title']); ?></h1>

								<?php
								$WIDGET=App\Http\Controllers\Controller::getWidget("usp");
								?>

								<?php $__currentLoopData = $WIDGET; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $DATA): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
								<?php
								$DETAILS=json_decode($DATA->widget_details,true);
								?>

								<div class="feature-panel">
									<div class="feature-box align-items-center counters counters-sm">
										<img src="<?php echo e(secure_url('public/'.$DETAILS['featured_image'])); ?>" alt="<?php echo e($DATA->widget_title); ?>" class="img-fluid">
										<div class="counter">
											<strong data-to="<?php echo e($DETAILS['number']); ?>" data-append="<?php echo e($DETAILS['suffix']); ?>">0</strong>
										</div>
									</div>
									<p><?php echo e($DATA->widget_title); ?> </p>
								</div>

								<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>



<section class="section expert-section expert-section-bg">
	<div class="container-fluid">
		<div class="row justify-content-end">
			<div class="col-lg-10 px-lg-0">
				<div class="expert-card-area">
					<h1 class="text-white"><?php echo e($PAGE_DATA['expert_section_title']); ?></h1>
					<div class="expert-image-card position-relative">
						<img src="<?php echo e(secure_url('public/'.$PAGE_DATA['expert_section_image'])); ?>" alt="<?php echo e($PAGE_DATA['expert_section_title']); ?>" class="img-fluid">
						<div class="expert-image-content-area">
							<div class="card-image-content">
								<?php echo $PAGE_DATA['expert_section_content']; ?>

							</div>
							<div class="border-bar margin-top"></div>
							<a href="<?php echo e($PAGE_DATA['expert_section_button_url']); ?>" class="text-decoration-none"><?php echo e($PAGE_DATA['expert_section_button_text']); ?></a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>



<section class="speciality-section">
	<div class="section-spacing">
		<div class="container">
			<div class="row">
				<div class="col-lg-6 col-md-6">
					<div class="section-title">
						<h1><?php echo e($PAGE_DATA['speciality_section_title']); ?></h1>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 text-end viewall-btn desktop-block">
					<a href="<?php echo e($PAGE_DATA['speciality_section_button_url']); ?>" class="text-black"><?php echo e($PAGE_DATA['speciality_section_button_text']); ?> <i><img src="img/right-arrow.svg" alt="Right Arrow"></i></a>
				</div>
			</div>
			<?php
			$SPECIALITY_FEATURED=App\Http\Controllers\Controller::getSpecialityFeatured();
			?>
			<div class="speciality-wrap position-relative">
				<div class="row">
					<?php $__currentLoopData = $SPECIALITY_FEATURED; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $DATA): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
					<div class="custom-col mb-lg-3 mb-md-3 mb-3 mobile-border-right">
						<?php if($DATA->speciality_viewing_mode=="0"): ?>
						<a href="javascript:void(0)" onClick="showPopup('speciality',<?php echo e($DATA->speciality_id); ?>)" class="text-decoration-none">
							<?php else: ?>

							<a href="<?php echo e(secure_url('speciality/'.$DATA->speciality_slug)); ?>" class="text-decoration-none">
								<?php endif; ?>
								<div class="hospital-speciality-card text-center">
									<div class="hospital-speciality-icon">
										<img src="<?php echo e(secure_url('public/'.$DATA->speciality_icon_image)); ?>" alt="<?php echo e($DATA->speciality_title); ?>" class="img-fluid">
									</div>
									<div class="hospital-speciality-content">
										<h2><?php echo e($DATA->speciality_title); ?></h2>
									</div>
								</div>
							</a>
					</div>
					<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
					<div class="col-12 viewall-btn mobile-block mt-3 text-center">
						<a href="<?php echo e($PAGE_DATA['speciality_section_button_url']); ?>" class="text-black"><?php echo e($PAGE_DATA['speciality_section_button_text']); ?> <i><img src="img/right-arrow.svg" alt="Right Arrow"></i></a>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>



<section class="section-condition section section-bg section-main pb-lg-5 pb-3">
	<div class="bg-white main-background first"></div>
	<div class="centerContainer position-relative tripleMargin">
		<div class="section-title mb-lg-4 pb-lg-4">
			<h1><?php echo e($PAGE_DATA['condition_treatment_section_title']); ?></h1>
		</div>
	</div>
	<div class="centerContainer position-relative margin container-wrap d-flex" id="homePageCondition">
		<div class="sideBackground bCream2"></div>
		<div class="left order-lg-1 order-2">
			<div class="ml2 w30 tripleMargin left-mobile-width">
				<div class="diasease-content button-section mb-lg-5 mb-3">
					<?php echo $PAGE_DATA['condition_treatment_section_content']; ?>

					<a href="<?php echo e($PAGE_DATA['condition_treatment_section_button_url']); ?>"><?php echo e($PAGE_DATA['condition_treatment_section_button_text']); ?></a>
				</div>
			</div>

			<div class="condition-treatment-wrap d-flex align-items-end">
				<div class="expert-image">
					<img src="<?php echo e(secure_url('public/'.$PAGE_DATA['condition_treatment_expert_section_image'])); ?>" alt="<?php echo e($PAGE_DATA['condition_treatment_expert_section_title']); ?>" class="img-fluid">
				</div>
				<div class="expert-image-content">
					<a href="<?php echo e(secure_url('doctor')); ?>">
						<h2><?php echo e($PAGE_DATA['condition_treatment_expert_section_title']); ?></h2>
					</a>
					<?php echo $PAGE_DATA['condition_treatment_expert_section_content']; ?>

				</div>
			</div>
		</div>
		<div class="right position-relative order-lg-2 order-1">
			<div class="disease-feature-image position-relative overflow-hidden">
				<img src="<?php echo e(secure_url('public/'.$PAGE_DATA['condition_treatment_section_image'])); ?>" alt="<?php echo e($PAGE_DATA['condition_treatment_section_title']); ?>" class="img-fluid">
			</div>
		</div>
	</div>

	<div class="container relative" id="homeDivine">
		<div class="position-relative">
			<div class="row justify-content-center">
				<div class="col-lg-7 col-md-6 col-sm-12 col-12">
					<div class="procedure-bg procedure-feature thumb-home-text">
						<h1 class="mobile-heading mb-3"><?php echo e($PAGE_DATA['condition_treatment_procedure_section_title']); ?></h1>
						<img src="<?php echo e(secure_url('public/'.$PAGE_DATA['condition_treatment_procedure_section_image'])); ?>" alt="<?php echo e($PAGE_DATA['condition_treatment_procedure_section_title']); ?>" class="img-fluid">
						<div class="thumb-wrapper thumb-layout thumb-position">
							<div class="thumb-wrapper-content thumb-home-text">
								<a href="<?php echo e(secure_url('procedure')); ?>" class="text-decoration-none d-block">
									<h2><?php echo e($PAGE_DATA['condition_treatment_procedure_section_title']); ?></h2>
								</a>
							</div>
						</div>
						<div class="procedure-content pt-2 pt-lg-0">
							<?php echo $PAGE_DATA['condition_treatment_procedure_section_content']; ?>

							<a href="<?php echo e($PAGE_DATA['condition_treatment_procedure_section_button_url']); ?>"><?php echo e($PAGE_DATA['condition_treatment_procedure_section_button_text']); ?></a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>



<section class="section-gradient-bg report-section">
	<div class="section-spacing section7-line-bg">
		<div class="container">
			<div class="row justify-content-center">
				<div class="portal-wrap">
					<div class="patient-portal">
						<div class="patient-portal-img overflow-hidden">
							<a href="<?php echo e(secure_url('my-report')); ?>">
								<img src="<?php echo e(secure_url('public/'.$PAGE_DATA['patient_portal_section_image'])); ?>" alt="<?php echo e($PAGE_DATA['patient_portal_section_title']); ?>" class="img-fluid"></a>
						</div>
						<div class="patient-portal-round"></div>
					</div>
					<div class="roundCta patient-portal-content text-center d-flex flex-column justify-content-center align-items-center">
						<div class="border-bar"></div>
						<h2><?php echo e($PAGE_DATA['patient_portal_section_title']); ?></h2>
						<div class="border-bar"></div>
						<a href="<?php echo e($PAGE_DATA['patient_portal_section_button_url']); ?>"><?php echo e($PAGE_DATA['patient_portal_section_button_text']); ?></a>
						<img src="<?php echo e(secure_url('public/'.$PAGE_DATA['patient_portal_section_logo'])); ?>" alt="<?php echo e($PAGE_DATA['patient_portal_section_title']); ?>" class="img-fluid">
					</div>
				</div>
			</div>
		</div>
	</div>
</section>



<!-- <section class="search-section search-bg">

	<div class="search-line-bg py-5">

		<div class="container">

			<div class="row justify-content-between">

				<div class="col-xl-5 col-lg-6 mb-lg-0 mb-md-3 mb-sm-3 mb-3 ps-xl-6">

					<div class="alphabet-button-wrap">

						<h3>Find diseases & procedures by first letter</h3>

						<div class="select-option">

							<?php

							$alphabet = range('A', 'Z');

							$i=0;

							?>

							<?php $__currentLoopData = $alphabet; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $letter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

							<button class="btn-option btn-size <?php echo e($i++==0?'active':''); ?>" onclick="window.location.href='<?php echo e(secure_url('execute/filter-disease-by-letter/'.$letter)); ?>'"><?php echo e($letter); ?></button>

							<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

						</div>

					</div>

				</div>

				<div class="col-lg-5 mt-lg-auto">

					<div class="home-form-section">

						<h3>Search diseases & procedures</h3>

						<form action="<?php echo e(secure_url('search')); ?>" method="post" class="contact-form form-fields-rounded">

							<?php echo csrf_field(); ?>

							<div class="search-area position-relative">

								<i class="icon-magnifier search-icon"></i>

								<input type="text" value="" name="search_data" class="form-control" placeholder="Search" required>

							</div>

						</form>

					</div>

				</div>

			</div>

		</div>

	</div>

</section> -->



<section class="section testimonial-section">
	<div class="container">
		<div class="row">
			<div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
				<div class="section-title">
					<h1><?php echo e($PAGE_DATA['testimonial_section_title']); ?></h1>
				</div>
			</div>
			<div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-end">
				<div class="viewall-btn desktop-block">
					<a href="<?php echo e($PAGE_DATA['testimonial_section_button_url']); ?>" class="text-black"><?php echo e($PAGE_DATA['testimonial_section_button_text']); ?> <i><img src="img/right-arrow.svg" alt="Right Arrow"></i></a>
				</div>
			</div>
		</div>

		<?php

		$TESTIMONIAL_FEATURED=App\Http\Controllers\Controller::getTestimonialFeatured();

		?>

		<div class="row">
			<div class="col-12">
				<div class="owl-carousel owl-theme mb-0" data-plugin-options="{'responsive': {'0': {'items': 1}, '479': {'items': 1}, '768': {'items': 1}, '979': {'items': 1}, '1199': {'items': 1}}, 'loop': false, 'autoplay': false, 'dots': true, 'autoHeight': false, 'margin': 10}">
					<?php $__currentLoopData = $TESTIMONIAL_FEATURED; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $DATA): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
					<div class="row justify-content-between gy-lg-5">
						<div class="col-xl-6 col-lg-5 col-md-6 col-sm-12 col-12 order-2 order-lg-1 order-md-1">
							<div class="testimonial testimonial-style-2 testimonial-style-custom mb-0">
								<blockquote>
									<?php echo $DATA->testimonial_details; ?>

								</blockquote>
								<div class="testimonial-author text-start">
									<div class="testimonial-author-thumbnail">
										<img src="<?php echo e(secure_url('public/'.$DATA->testimonial_image)); ?>" alt="<?php echo e($DATA->testimonial_title); ?>" class="img-fluid border-bottom-left-radius-50">
									</div>
									<p class="text-start ms-3">
										<span class="text-4-5 mb-2 text-black font-weight-medium negative-ls-1"><?php echo e($DATA->testimonial_title); ?></span>
										<span class="text-black text-3-4 text-start mb-0"><?php echo e($DATA->testimonial_location); ?></span>
									</p>
								</div>
							</div>
						</div>
						<div class="col-xl-5 col-lg-5 col-md-6 col-sm-12 col-12 text-center order-1 order-lg-2 position-relative testimonial-video mb-lg-0 mb-md-4 mb-sm-4 mb-4" onClick="playVideoTestimonial('<?php echo e($DATA->testimonial_video_type); ?>','<?php echo e($DATA->testimonial_video_id); ?>',this)">
							<img src="<?php echo e(secure_url('public/'.$DATA->testimonial_video_thumbnail)); ?>" class="img-fluid border-bottom-left-radius-135" alt="<?php echo e($DATA->testimonial_title); ?>">
							<div class="play-icon">
								<img src="img/play-icon-small.png" alt="Play Icon" class="img-fluid">
							</div>
						</div>
					</div>
					<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
				</div>
			</div>
		</div>
	</div>
</section>



<section class="section blog-section section-border section-gradient-secondary mobile-bg">
	<div class="container">
		<div class="row">
			<div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 mb-lg-0 mb-0">
				<div class="section-title">
					<h1><?php echo e($PAGE_DATA['blog_section_title']); ?></h1>
				</div>
			</div>
			<div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-end">
				<div class="viewall-btn desktop-block">
					<a href="<?php echo e($PAGE_DATA['blog_section_button_url']); ?>" class="text-black"><?php echo e($PAGE_DATA['blog_section_button_text']); ?> <i><img src="img/right-arrow.svg" alt="Right Arrow"></i></a>
				</div>
			</div>
		</div>

		<?php

		$BLOGS=App\Http\Controllers\Controller::getBlogLimit(5);

		?>

		<div class="row justify-content-between">
			<div class="col-xl-5 col-lg-6 col-md-6 col-sm-12 col-12 mb-lg-0 mb-md-0 mb-sm-4 mb-4">
				<?php for($i=0;$i<1;$i++): ?> <?php if(!empty($BLOGS[$i])): ?> <div class="blogcard-feature-wrap position-relative">
					<div class="blog-feature-item position-relative overflow-hidden">
						<a href="<?php echo e(secure_url('blog/'.$BLOGS[$i]->blog_slug)); ?>">
							<img src="<?php echo e(secure_url('public/'.$BLOGS[$i]->blog_image)); ?>" alt="<?php echo e($BLOGS[$i]->blog_title); ?>" class="img-fluid">
							<div class="blog-feature-content">
								<h2><?php echo e($BLOGS[$i]->blog_title); ?></h2>
								<p>

									<?php if(!empty($BLOGS[$i]->blog_doctor_id)): ?>

									<?php if(!empty(App\Http\Controllers\Controller::getDoctorById($BLOGS[$i]->blog_doctor_id))): ?>

									By

									<?php echo e(App\Http\Controllers\Controller::getDoctorById($BLOGS[$i]->blog_doctor_id)[0]->doctor_name); ?>


									<?php endif; ?>

									<?php endif; ?>

								</p>
							</div>
						</a>
					</div>
			</div>

			<?php endif; ?>

			<?php endfor; ?>

		</div>

		<div class="col-xl-7 col-lg-6 col-md-6 col-sm-12 col-12">
			<div class="blogcard-list-wrap d-lg-block d-none">
				<div class="row">
					<?php for($i=1;$i<5;$i++): ?> <?php if(!empty($BLOGS[$i])): ?> <div class="col-xl-6 col-lg-6 col-md-6 mb-3">
						<div class="blog-list-item position-relative overflow-hidden">
							<a href="<?php echo e(secure_url('blog/'.$BLOGS[$i]->blog_slug)); ?>">
								<img src="<?php echo e(secure_url('public/'.$BLOGS[$i]->blog_image)); ?>" alt="<?php echo e($BLOGS[$i]->blog_title); ?>" class="img-fluid">
								<div class="blog-list-content">
									<h2><?php echo e($BLOGS[$i]->blog_title); ?></h2>
									<p>
										<?php if(!empty($BLOGS[$i]->blog_doctor_id)): ?>

										<?php if(!empty(App\Http\Controllers\Controller::getDoctorById($BLOGS[$i]->blog_doctor_id))): ?>

										By

										<?php echo e(App\Http\Controllers\Controller::getDoctorById($BLOGS[$i]->blog_doctor_id)[0]->doctor_name); ?>


										<?php endif; ?>

										<?php endif; ?>
									</p>
								</div>
							</a>
						</div>
				</div>

				<?php endif; ?>

				<?php endfor; ?>

			</div>
		</div>

		<div class="d-lg-none d-block">
			<div class="owl-carousel owl-theme mb-0" data-plugin-options="{'responsive': {'0': {'items': 1}, '479': {'items': 1}, '768': {'items': 2}, '979': {'items': 3}, '1199': {'items': 3}}, 'loop': false, 'autoplay': true, 'autoHeight': true, 'margin': 10}">
				<?php for($i=0;$i<5;$i++): ?> <?php if(!empty($BLOGS[$i])): ?> <div class="blog-list-item position-relative overflow-hidden">
					<a href="<?php echo e(secure_url('blog/'.$BLOGS[$i]->blog_slug)); ?>">
						<img src="<?php echo e(secure_url('public/'.$BLOGS[$i]->blog_image)); ?>" alt="<?php echo e($BLOGS[$i]->blog_title); ?>" class="img-fluid">
						<div class="blog-list-content">
							<h2><?php echo e($BLOGS[$i]->blog_title); ?></h2>
							<p>

								<?php if(!empty($BLOGS[$i]->blog_doctor_id)): ?>

								<?php if(!empty(App\Http\Controllers\Controller::getDoctorById($BLOGS[$i]->blog_doctor_id))): ?>

								By

								<?php echo e(App\Http\Controllers\Controller::getDoctorById($BLOGS[$i]->blog_doctor_id)[0]->doctor_name); ?>


								<?php endif; ?>

								<?php endif; ?>

							</p>
						</div>
					</a>
			</div>

			<?php endif; ?>

			<?php endfor; ?>

		</div>
	</div>
	</div>
	</div>
	</div>
</section>



<?php $__env->stopSection(); ?>



<?php $__env->startPush( 'styles' ); ?>

<style>
	
</style>


<?php $__env->stopPush(); ?>



<?php $__env->startPush( 'scripts' ); ?>


<?php $__env->stopPush(); ?>
<?php echo $__env->make( 'website.layouts.app' , \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH F:\var\www\html\resources\views/website/modules/template/home_page_template.blade.php ENDPATH**/ ?>