[2025-05-27 22:31:01] development.ERROR: Command "generate" is not defined.

Did you mean one of these?
    event:generate
    key:generate {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"generate\" is not defined.

Did you mean one of these?
    event:generate
    key:generate at F:\\var\\www\\html\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 F:\\var\\www\\html\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('generate')
#1 F:\\var\\www\\html\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 F:\\var\\www\\html\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 F:\\var\\www\\html\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
